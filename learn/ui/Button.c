#include "Button.h"
#include <string.h>

// Static rendering resources shared by all buttons
static ButtonRenderResources s_resources = {0};

// Shader loading function
static SDL_GPUShader* load_shader(SDL_GPUDevice* device, const char* filename, SDL_GPUShaderStage stage) {
    char fullPath[256];
    SDL_snprintf(fullPath, sizeof(fullPath), "build/assets/Compiled/MSL/%s", filename);

    size_t codeSize;
    void* code = SDL_LoadFile(fullPath, &codeSize);
    if (code == NULL) {
        SDL_Log("Failed to load shader from disk! %s", fullPath);
        return NULL;
    }

    SDL_GPUShaderCreateInfo shaderInfo = {
        .code = code,
        .code_size = codeSize,
        .entrypoint = "main0", // MSL uses main0
        .format = SDL_GPU_SHADERFORMAT_MSL,
        .stage = stage,
        .num_samplers = 0,
        .num_uniform_buffers = (stage == SDL_GPU_SHADERSTAGE_FRAGMENT) ? 1 : 0,
        .num_storage_buffers = 0,
        .num_storage_textures = 0
    };

    SDL_GPUShader* shader = SDL_CreateGPUShader(device, &shaderInfo);
    if (shader == NULL) {
        SDL_Log("Failed to create shader!");
        SDL_free(code);
        return NULL;
    }

    SDL_free(code);
    return shader;
}

// Create button pipeline
static bool create_button_pipeline(ButtonRenderResources* resources) {
    // Load shaders
    resources->vertex_shader = load_shader(
        resources->device, "Button.vert.msl", SDL_GPU_SHADERSTAGE_VERTEX);
    if (resources->vertex_shader == NULL) {
        SDL_Log("Failed to create button vertex shader!");
        return false;
    }

    resources->fragment_shader = load_shader(
        resources->device, "Button.frag.msl", SDL_GPU_SHADERSTAGE_FRAGMENT);
    if (resources->fragment_shader == NULL) {
        SDL_Log("Failed to create button fragment shader!");
        SDL_ReleaseGPUShader(resources->device, resources->vertex_shader);
        resources->vertex_shader = NULL;
        return false;
    }

    // Create pipeline
    SDL_GPUGraphicsPipelineCreateInfo pipelineCreateInfo = {
        .target_info = {
            .num_color_targets = 1,
            .color_target_descriptions = (SDL_GPUColorTargetDescription[]){
                {
                    .format = SDL_GetGPUSwapchainTextureFormat(resources->device, NULL),
                    .blend_state = (SDL_GPUColorTargetBlendState){
                        .enable_blend = true,
                        .alpha_blend_op = SDL_GPU_BLENDOP_ADD,
                        .color_blend_op = SDL_GPU_BLENDOP_ADD,
                        .color_write_mask = 0xF,
                        .src_alpha_blendfactor = SDL_GPU_BLENDFACTOR_SRC_ALPHA,
                        .dst_alpha_blendfactor = SDL_GPU_BLENDFACTOR_DST_ALPHA,
                        .src_color_blendfactor = SDL_GPU_BLENDFACTOR_SRC_ALPHA,
                        .dst_color_blendfactor = SDL_GPU_BLENDFACTOR_ONE_MINUS_SRC_ALPHA
                    }
                }
            },
        },
        // This is set up to match the vertex shader layout!
        .vertex_input_state = (SDL_GPUVertexInputState){
            .num_vertex_buffers = 1,
            .vertex_buffer_descriptions = (SDL_GPUVertexBufferDescription[]){{
                .slot = 0,
                .input_rate = SDL_GPU_VERTEXINPUTRATE_VERTEX,
                .pitch = sizeof(PositionColorVertex)
            }},
            .num_vertex_attributes = 2, // Position and color
            .vertex_attributes = (SDL_GPUVertexAttribute[]){
                {
                    .buffer_slot = 0,
                    .format = SDL_GPU_VERTEXELEMENTFORMAT_FLOAT3,
                    .location = 0,
                    .offset = 0
                },
                {
                    .buffer_slot = 0,
                    .format = SDL_GPU_VERTEXELEMENTFORMAT_UBYTE4_NORM,
                    .location = 1,
                    .offset = sizeof(float) * 3
                }
            }
        },
        .primitive_type = SDL_GPU_PRIMITIVETYPE_TRIANGLELIST,
        .vertex_shader = resources->vertex_shader,
        .fragment_shader = resources->fragment_shader
    };

    resources->pipeline = SDL_CreateGPUGraphicsPipeline(resources->device, &pipelineCreateInfo);
    if (resources->pipeline == NULL) {
        SDL_Log("Failed to create button pipeline: %s", SDL_GetError());
        SDL_ReleaseGPUShader(resources->device, resources->vertex_shader);
        SDL_ReleaseGPUShader(resources->device, resources->fragment_shader);
        resources->vertex_shader = NULL;
        resources->fragment_shader = NULL;
        return false;
    }

    return true;
}

// Convert screen coordinates to normalized device coordinates
static void screen_to_ndc(float screen_x, float screen_y, int screen_width, int screen_height, float *ndc_x, float *ndc_y) {
    *ndc_x = (screen_x / screen_width) * 2.0f - 1.0f;
    *ndc_y = 1.0f - (screen_y / screen_height) * 2.0f; // Flip Y axis
}

// Create a rectangle in vertex buffer
static void create_rectangle(PositionColorVertex *vertices, Uint16 *indices, int rect_index,
                             float x, float y, float width, float height,
                             int screen_width, int screen_height,
                             float r, float g, float b, float a) {
    int vertex_offset = rect_index * 4;
    int index_offset = rect_index * 6;

    float ndc_x, ndc_y, ndc_x2, ndc_y2;
    screen_to_ndc(x, y, screen_width, screen_height, &ndc_x, &ndc_y);
    screen_to_ndc(x + width, y + height, screen_width, screen_height, &ndc_x2, &ndc_y2);

    // Top-left
    vertices[vertex_offset + 0].x = ndc_x;
    vertices[vertex_offset + 0].y = ndc_y;
    vertices[vertex_offset + 0].z = 0.0f;
    vertices[vertex_offset + 0].r = (Uint8)(r * 255);
    vertices[vertex_offset + 0].g = (Uint8)(g * 255);
    vertices[vertex_offset + 0].b = (Uint8)(b * 255);
    vertices[vertex_offset + 0].a = (Uint8)(a * 255);

    // Top-right
    vertices[vertex_offset + 1].x = ndc_x2;
    vertices[vertex_offset + 1].y = ndc_y;
    vertices[vertex_offset + 1].z = 0.0f;
    vertices[vertex_offset + 1].r = (Uint8)(r * 255);
    vertices[vertex_offset + 1].g = (Uint8)(g * 255);
    vertices[vertex_offset + 1].b = (Uint8)(b * 255);
    vertices[vertex_offset + 1].a = (Uint8)(a * 255);

    // Bottom-right
    vertices[vertex_offset + 2].x = ndc_x2;
    vertices[vertex_offset + 2].y = ndc_y2;
    vertices[vertex_offset + 2].z = 0.0f;
    vertices[vertex_offset + 2].r = (Uint8)(r * 255);
    vertices[vertex_offset + 2].g = (Uint8)(g * 255);
    vertices[vertex_offset + 2].b = (Uint8)(b * 255);
    vertices[vertex_offset + 2].a = (Uint8)(a * 255);

    // Bottom-left
    vertices[vertex_offset + 3].x = ndc_x;
    vertices[vertex_offset + 3].y = ndc_y2;
    vertices[vertex_offset + 3].z = 0.0f;
    vertices[vertex_offset + 3].r = (Uint8)(r * 255);
    vertices[vertex_offset + 3].g = (Uint8)(g * 255);
    vertices[vertex_offset + 3].b = (Uint8)(b * 255);
    vertices[vertex_offset + 3].a = (Uint8)(a * 255);

    // Indices for two triangles
    Uint16 base = vertex_offset;
    indices[index_offset + 0] = base + 0;
    indices[index_offset + 1] = base + 1;
    indices[index_offset + 2] = base + 2;
    indices[index_offset + 3] = base + 0;
    indices[index_offset + 4] = base + 2;
    indices[index_offset + 5] = base + 3;
}

// Initialize button rendering resources
bool Button_InitRenderResources(SDL_GPUDevice* device, int max_buttons) {
    if (s_resources.initialized) {
        return true; // Already initialized
    }

    s_resources.device = device;
    s_resources.max_buttons = max_buttons;
    
    // Allocate memory for vertices and indices
    size_t vertex_data_size = sizeof(PositionColorVertex) * 4 * max_buttons;
    size_t index_data_size = sizeof(Uint16) * 6 * max_buttons;

    s_resources.vertices = (PositionColorVertex*)SDL_malloc(vertex_data_size);
    s_resources.indices = (Uint16*)SDL_malloc(index_data_size);
    
    if (!s_resources.vertices || !s_resources.indices) {
        SDL_Log("Failed to allocate memory for button vertices/indices");
        Button_CleanupRenderResources();
        return false;
    }
    
    // Create vertex buffer
    s_resources.vertex_buffer = SDL_CreateGPUBuffer(
        device,
        &(SDL_GPUBufferCreateInfo){
            .usage = SDL_GPU_BUFFERUSAGE_VERTEX,
            .size = vertex_data_size
        }
    );

    if (!s_resources.vertex_buffer) {
        SDL_Log("Failed to create button vertex buffer!");
        Button_CleanupRenderResources();
        return false;
    }

    // Create index buffer
    s_resources.index_buffer = SDL_CreateGPUBuffer(
        device,
        &(SDL_GPUBufferCreateInfo){
            .usage = SDL_GPU_BUFFERUSAGE_INDEX,
            .size = index_data_size
        }
    );

    if (!s_resources.index_buffer) {
        SDL_Log("Failed to create button index buffer!");
        Button_CleanupRenderResources();
        return false;
    }

    // Create transfer buffer
    s_resources.transfer_buffer = SDL_CreateGPUTransferBuffer(
        device,
        &(SDL_GPUTransferBufferCreateInfo){
            .usage = SDL_GPU_TRANSFERBUFFERUSAGE_UPLOAD,
            .size = vertex_data_size + index_data_size
        }
    );

    if (!s_resources.transfer_buffer) {
        SDL_Log("Failed to create button transfer buffer!");
        Button_CleanupRenderResources();
        return false;
    }

    // Note: We don't need a uniform buffer since SDL3 uses push constants

    // Create button pipeline
    if (!create_button_pipeline(&s_resources)) {
        SDL_Log("Failed to create button pipeline!");
        Button_CleanupRenderResources();
        return false;
    }

    s_resources.initialized = true;
    return true;
}

// Clean up button rendering resources
void Button_CleanupRenderResources() {
    if (s_resources.pipeline) {
        SDL_ReleaseGPUGraphicsPipeline(s_resources.device, s_resources.pipeline);
        s_resources.pipeline = NULL;
    }
    
    if (s_resources.vertex_shader) {
        SDL_ReleaseGPUShader(s_resources.device, s_resources.vertex_shader);
        s_resources.vertex_shader = NULL;
    }
    
    if (s_resources.fragment_shader) {
        SDL_ReleaseGPUShader(s_resources.device, s_resources.fragment_shader);
        s_resources.fragment_shader = NULL;
    }
    
    if (s_resources.transfer_buffer) {
        SDL_ReleaseGPUTransferBuffer(s_resources.device, s_resources.transfer_buffer);
        s_resources.transfer_buffer = NULL;
    }

    // No uniform buffer to clean up

    if (s_resources.vertex_buffer) {
        SDL_ReleaseGPUBuffer(s_resources.device, s_resources.vertex_buffer);
        s_resources.vertex_buffer = NULL;
    }

    if (s_resources.index_buffer) {
        SDL_ReleaseGPUBuffer(s_resources.device, s_resources.index_buffer);
        s_resources.index_buffer = NULL;
    }
    
    if (s_resources.vertices) {
        SDL_free(s_resources.vertices);
        s_resources.vertices = NULL;
    }
    
    if (s_resources.indices) {
        SDL_free(s_resources.indices);
        s_resources.indices = NULL;
    }
    
    s_resources.initialized = false;
}

// Initialize a button with default properties
void Button_Init(Button *button, float x, float y, float width, float height, const char *text) {
    button->rect = (SDL_FRect){x, y, width, height};
    button->color = (SDL_FColor){0.4f, 0.4f, 0.8f, 1.0f};       // Blue
    button->hoverColor = (SDL_FColor){0.5f, 0.5f, 0.9f, 1.0f};  // Lighter blue
    button->textColor = (SDL_FColor){1.0f, 1.0f, 1.0f, 1.0f};   // White
    button->text = (char *)text;
    button->isHovered = false;
    button->isPressed = false;
    button->isDirty = true;  // Initially dirty to ensure first render
}

// Update button position (e.g., after window resize)
void Button_UpdatePosition(Button *button, float x, float y) {
    if (button->rect.x != x || button->rect.y != y) {
        button->rect.x = x;
        button->rect.y = y;
        button->isDirty = true;
    }
}

// Check if a point is inside the button
bool Button_ContainsPoint(Button *button, float x, float y) {
    return (x >= button->rect.x && x <= button->rect.x + button->rect.w &&
            y >= button->rect.y && y <= button->rect.y + button->rect.h);
}

// Handle mouse motion event
void Button_HandleMouseMotion(Button *button, float x, float y) {
    bool wasHovered = button->isHovered;
    button->isHovered = Button_ContainsPoint(button, x, y);
    
    // Mark as dirty if hover state changed
    if (wasHovered != button->isHovered) {
        button->isDirty = true;
    }
}

// Handle mouse button down event
bool Button_HandleMouseButtonDown(Button *button, float x, float y, Uint8 button_type) {
    if (button_type == SDL_BUTTON_LEFT) {
        if (Button_ContainsPoint(button, x, y)) {
            if (!button->isPressed) {
                button->isPressed = true;
                button->isDirty = true;
                return true;
            }
            return true;
        }
    }
    return false;
}

// Handle mouse button up event
bool Button_HandleMouseButtonUp(Button *button, float x, float y, Uint8 button_type) {
    bool wasClicked = false;

    if (button_type == SDL_BUTTON_LEFT && button->isPressed) {
        if (Button_ContainsPoint(button, x, y)) {
            // Button clicked!
            wasClicked = true;
        }
        button->isPressed = false;
        button->isDirty = true;
    }

    return wasClicked;
}

// Get the current color of the button (based on hover state)
SDL_FColor Button_GetCurrentColor(Button *button) {
    return button->isHovered ? button->hoverColor : button->color;
}

// Mark the button as dirty (needing visual update)
void Button_MarkDirty(Button *button) {
    button->isDirty = true;
}

// Prepare button vertex data for rendering
void Button_PrepareVertexData(Button* button, int button_index, int screen_width, int screen_height) {
    if (!s_resources.initialized || button_index >= s_resources.max_buttons) {
        return;
    }

    // Only update vertex data if button is dirty
    if (button->isDirty) {
        // Get button color based on state
        SDL_FColor buttonColor = Button_GetCurrentColor(button);

        // Create button rectangle
        create_rectangle(
            s_resources.vertices, s_resources.indices, button_index,
            button->rect.x, button->rect.y,
            button->rect.w, button->rect.h,
            screen_width, screen_height,
            buttonColor.r, buttonColor.g, buttonColor.b, buttonColor.a
        );

        // Clear dirty flag
        button->isDirty = false;
    }
}

// Upload button geometry data to GPU
void Button_UploadGeometryData(SDL_GPUCommandBuffer* cmd_buf) {
    if (!s_resources.initialized || !cmd_buf) {
        return;
    }
    
    // Map transfer buffer
    void *transfer_data = SDL_MapGPUTransferBuffer(s_resources.device, s_resources.transfer_buffer, false);
    if (!transfer_data) {
        SDL_Log("Failed to map button transfer buffer!");
        return;
    }
    
    // Calculate data sizes
    size_t vertex_data_size = sizeof(PositionColorVertex) * 4 * s_resources.max_buttons;
    size_t index_data_size = sizeof(Uint16) * 6 * s_resources.max_buttons;
    
    // Copy vertex and index data
    memcpy(transfer_data, s_resources.vertices, vertex_data_size);
    memcpy((char*)transfer_data + vertex_data_size, s_resources.indices, index_data_size);
    
    SDL_UnmapGPUTransferBuffer(s_resources.device, s_resources.transfer_buffer);
    
    // Start copy pass
    SDL_GPUCopyPass *copy_pass = SDL_BeginGPUCopyPass(cmd_buf);
    
    // Upload vertex data
    SDL_UploadToGPUBuffer(
        copy_pass,
        &(SDL_GPUTransferBufferLocation){
            .transfer_buffer = s_resources.transfer_buffer,
            .offset = 0
        },
        &(SDL_GPUBufferRegion){
            .buffer = s_resources.vertex_buffer,
            .offset = 0,
            .size = vertex_data_size
        },
        false
    );
    
    // Upload index data
    SDL_UploadToGPUBuffer(
        copy_pass,
        &(SDL_GPUTransferBufferLocation){
            .transfer_buffer = s_resources.transfer_buffer,
            .offset = vertex_data_size
        },
        &(SDL_GPUBufferRegion){
            .buffer = s_resources.index_buffer,
            .offset = 0,
            .size = index_data_size
        },
        false
    );
    
    SDL_EndGPUCopyPass(copy_pass);
}

// Set button color uniform using push constants
void Button_SetColorUniform(SDL_GPUCommandBuffer* cmd_buf, Button* button) {
    // This function is now handled in the draw function using push constants
    (void)cmd_buf;
    (void)button;
}

// Draw buttons using the provided render pass and command buffer
void Button_Draw(SDL_GPURenderPass* render_pass, SDL_GPUCommandBuffer* cmd_buf, SDL_GPUGraphicsPipeline* external_pipeline, Button* buttons, int button_count) {
    if (!s_resources.initialized || !render_pass || !cmd_buf || !buttons || button_count <= 0) {
        return;
    }

    // Use our custom pipeline if available, otherwise use the provided one
    SDL_GPUGraphicsPipeline* pipeline = s_resources.pipeline ? s_resources.pipeline : external_pipeline;

    if (!pipeline) {
        SDL_Log("No valid pipeline for button rendering!");
        return;
    }

    // Bind pipeline and buffers
    SDL_BindGPUGraphicsPipeline(render_pass, pipeline);
    SDL_BindGPUVertexBuffers(
        render_pass, 0,
        &(SDL_GPUBufferBinding){.buffer = s_resources.vertex_buffer, .offset = 0},
        1
    );
    SDL_BindGPUIndexBuffer(
        render_pass,
        &(SDL_GPUBufferBinding){.buffer = s_resources.index_buffer, .offset = 0},
        SDL_GPU_INDEXELEMENTSIZE_16BIT
    );

    // Draw buttons
    for (int i = 0; i < button_count; i++) {
        // Color is already in the vertex data
        SDL_DrawGPUIndexedPrimitives(render_pass, 6, 1, i * 6, 0, 0);
    }
}