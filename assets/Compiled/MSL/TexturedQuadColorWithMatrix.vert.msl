#include <metal_stdlib>
#include <simd/simd.h>

using namespace metal;

struct type_UniformBlock
{
    float4x4 MatrixTransform;
};

struct main0_out
{
    float2 out_var_TEXCOORD0 [[user(locn0)]];
    float4 out_var_TEXCOORD1 [[user(locn1)]];
    float4 gl_Position [[position]];
};

struct main0_in
{
    float4 in_var_TEXCOORD0 [[attribute(0)]];
    float2 in_var_TEXCOORD1 [[attribute(1)]];
    float4 in_var_TEXCOORD2 [[attribute(2)]];
};

vertex main0_out main0(main0_in in [[stage_in]], constant type_UniformBlock& UniformBlock [[buffer(0)]])
{
    main0_out out = {};
    out.out_var_TEXCOORD0 = in.in_var_TEXCOORD1;
    out.out_var_TEXCOORD1 = in.in_var_TEXCOORD2;
    out.gl_Position = UniformBlock.MatrixTransform * in.in_var_TEXCOORD0;
    return out;
}

